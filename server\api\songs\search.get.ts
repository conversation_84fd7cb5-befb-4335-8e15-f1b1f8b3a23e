import axios from 'axios'

export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const searchQuery = (query.q as string) || ''
  const page = Number.parseInt(query.page as string, 10) || 1
  const limit = Number.parseInt(query.limit as string, 10) || 30
  const include = (query.include as string) || ''

  let params: any = {
    search: searchQuery,
    _embed: true,
    per_page: limit,
    page: page,
  }

  if (include) {
    params = {
      include: include,
      _embed: true,
    }
  }

  try {
    const response = await axios.get('https://www.dochord.com/wp-json/wp/v2/posts', {
      params: params,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
    })

    // แปลงข้อมูลจาก dochord.com ให้เป็นรูปแบบที่เราต้องการ
    const transformedSongs = response.data.map((post: any) => {
      const title = post.title?.rendered || ''

      // ดึงข้อมูลศิลปินจาก _embedded.wp:term
      const artistInfo = post._embedded?.['wp:term']?.find((termGroup: any[]) =>
        termGroup.some((term: any) => term.taxonomy === 'artist'),
      )?.find((term: any) => term.taxonomy === 'artist')

      // ดึงรูปภาพจาก _embedded.wp:featuredmedia
      const featuredImage = post._embedded?.['wp:featuredmedia']?.[0]?.source_url || ''

      return {
        id: post.id.toString(),
        title: title.replace(/<[^>]*>/g, ''),
        artist: artistInfo?.name || 'Unknown Artist',
        artist_slug: artistInfo?.slug || '',
        thumbnail: featuredImage,
      }
    })

    const totalCount = Number.parseInt(response.headers['x-wp-total'] as string, 10) || transformedSongs.length

    if (include) {
      return {
        count: transformedSongs.length,
        limit: limit,
        page: page,
        total: totalCount,
        items: transformedSongs.sort((a: any, b: any) => {
          return include.indexOf(a.id) - include.indexOf(b.id)
        }),
      }
    }

    return {
      count: transformedSongs.length,
      limit: limit,
      page: page,
      total: totalCount,
      items: transformedSongs,
    }
  } catch (error) {
    event.node.res.statusCode = 500
    console.error('Error fetching from dochord.com:', error)

    return {
      code: '500',
      message: 'Failed to fetch',
    }
  }
})
