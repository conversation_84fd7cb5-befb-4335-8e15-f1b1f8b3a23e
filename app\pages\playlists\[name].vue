<template>
  <div class="playlist-detail-page">
    <!-- Loading State -->
    <div
      v-if="isLoading || song.fetch.status.isLoading"
      class="animate-pulse"
    >
      <div class="mb-4 h-4 w-1/4 rounded bg-gray-200" />
      <div class="space-y-4">
        <div
          v-for="i in 5"
          :key="i"
          class="h-16 rounded bg-gray-200"
        />
      </div>
    </div>

    <!-- Error State -->
    <Card
      v-else-if="error"
      class="py-12 text-center"
    >
      <Icon
        name="lucide:alert-circle"
        class="mx-auto mb-4 h-16 w-16 text-red-500"
      />
      <h2 class="mb-2 text-2xl font-bold text-gray-900">
        ไม่พบเพลย์ลิสต์
      </h2>
      <p class="mb-6 text-gray-600">
        {{ error }}
      </p>
      <Button
        to="/playlists"
        color="neutral"
        variant="outline"
        icon="lucide:arrow-left"
      >
        กลับไปยังเพลย์ลิสต์
      </Button>
    </Card>

    <!-- Playlist Content -->
    <div v-else-if="currentPlaylist">
      <!-- Playlist Header -->
      <div class="mb-4">
        <div class="mb-4 flex items-start justify-between">
          <div>
            <h1 class="mb-2 text-3xl font-bold text-gray-900">
              {{ currentPlaylist.name }}
            </h1>
            <p class="text-gray-600">
              {{ playlistSongs.length }} เพลง • แก้ไขล่าสุด
              {{ formatDate(currentPlaylist.updatedAt || currentPlaylist.createdAt) }}
            </p>
          </div>

          <div class="flex gap-2">
            <Button
              variant="outline"
              icon="lucide:share"
              @click="sharePlaylist"
            >
              แชร์
            </Button>

            <DropdownMenu :items="getPlaylistActions()">
              <Button
                variant="outline"
                icon="i-lucide-more-vertical"
              />
            </DropdownMenu>
          </div>
        </div>

        <!-- Back Button -->
        <Button
          to="/playlists"
          color="neutral"
          variant="outline"
          icon="lucide:arrow-left"
          size="sm"
        >
          กลับไปยังเพลย์ลิสต์
        </Button>
      </div>

      <!-- Empty Playlist -->
      <Card
        v-if="playlistSongs.length === 0"
        class="py-12 text-center"
      >
        <Icon
          name="lucide:music"
          class="mx-auto mb-4 h-16 w-16 text-gray-400"
        />
        <h3 class="mb-2 text-lg font-medium text-gray-900">
          เพลย์ลิสต์ว่างเปล่า
        </h3>
        <p class="mb-6 text-gray-600">
          เริ่มเพิ่มเพลงลงในเพลย์ลิสต์นี้จากหน้าเพลงต่างๆ
        </p>
        <NuxtLink to="/songs">
          <Button
            color="primary"
            icon="lucide:search"
          >
            ค้นหาเพลง
          </Button>
        </NuxtLink>
      </Card>

      <!-- Songs List -->
      <div v-else>
        <!-- List Header -->
        <div class="mb-4 flex items-center justify-between">
          <p class="text-sm text-gray-600">
            ลากและวางเพื่อเรียงลำดับเพลง
          </p>
        </div>

        <!-- Draggable Songs List -->
        <draggable
          v-model="playlistSongs"
          item-key="id"
          handle=".drag-handle"
          ghost-class="ghost"
          chosen-class="chosen"
          drag-class="drag"
          @end="onSongReorder"
        >
          <template #item="{ element: song, index }">
            <div class="mb-3 rounded-lg bg-white shadow-sm transition-shadow duration-200 hover:shadow-md">
              <div class="flex items-center gap-4 p-4">
                <!-- Drag Handle -->
                <div class="drag-handle cursor-move text-gray-400 hover:text-gray-600">
                  <Icon
                    name="lucide:grip-vertical"
                    class="h-5 w-5"
                  />
                </div>
                <!-- Song Thumbnail -->
                <div class="flex-shrink-0">
                  <Image
                    class="h-12 w-12 rounded-md object-cover"
                    :src="song.thumbnail"
                    :alt="song.title"
                  >
                    <template #error>
                      <div class="flex h-12 w-12 items-center justify-center rounded-md bg-gray-100">
                        <Icon
                          name="ph:music-notes"
                          class="h-6 w-6 text-gray-400"
                        />
                      </div>
                    </template>
                  </Image>
                </div>

                <!-- Song Info -->
                <div class="min-w-0 flex-1">
                  <h3 class="truncate font-semibold text-gray-900">
                    {{ song.title }}
                  </h3>
                  <p class="truncate text-sm text-gray-600">
                    {{ song.artist }}
                  </p>
                </div>

                <!-- Actions -->
                <div class="flex items-center gap-2">
                  <NuxtLink :to="`/song/${song.id}`">
                    <Button
                      size="sm"
                      variant="outline"
                      icon="lucide:external-link"
                    >
                      เปิด
                    </Button>
                  </NuxtLink>

                  <Button
                    size="sm"
                    variant="ghost"
                    icon="lucide:trash-2"
                    class="text-red-600 hover:text-red-700"
                    @click="confirmRemoveSong(song, index)"
                  />
                </div>
              </div>
            </div>
          </template>
        </draggable>
      </div>
    </div>

    <!-- Edit Playlist Modal -->
    <Modal
      v-model:page="showEditModal"
      title="แก้ไขชื่อเพลย์ลิสต์"
    >
      <template #body>
        <form @submit.prevent="updatePlaylistName">
          <div class="mb-4">
            <Label for="edit-playlist-name">
              ชื่อเพลย์ลิสต์
            </Label>
            <Input
              id="edit-playlist-name"
              v-model="editPlaylistName"
              placeholder="ใส่ชื่อเพลย์ลิสต์..."
              required
              class="mt-1"
            />
          </div>

          <div class="flex justify-end gap-3">
            <Button
              type="button"
              variant="outline"
              @click="showEditModal = false"
            >
              ยกเลิก
            </Button>
            <Button
              type="submit"
              color="primary"
              :loading="isUpdating"
            >
              บันทึก
            </Button>
          </div>
        </form>
      </template>
    </Modal>
  </div>
</template>

<script setup lang="ts">
import draggable from 'vuedraggable'
import type { PlaylistSong, PlaylistItem } from '~/composables/useSongLoader'

// Get route params
const route = useRoute()
const playlistName = route.params.name as string

// Page meta
useHead({
  title: `${decodeURIComponent(playlistName)} | Mhalong chords`,
  meta: [
    {
      name: 'description',
      content: `เพลย์ลิสต์ ${decodeURIComponent(playlistName)} - จัดการและเล่นเพลงในเพลย์ลิสต์ของคุณ`,
    },
  ],
})

const playlist = usePlaylist()
const dialog = useDialog()

const isLoading = ref(true)
const error = ref<string | null>(null)
const noti = useNotification()
const song = useSongSearchLoader()
const currentPlaylist = ref<PlaylistItem | null>(null)
const playlistSongs = ref<SongSmallItem[]>([])
const showEditModal = ref(false)
const editPlaylistName = ref('')
const isUpdating = ref(false)

// Check if this is a shared playlist
const isSharedPlaylist = computed(() => {
  return route.query.ids && typeof route.query.ids === 'string'
})

// Load playlist data
const loadPlaylistData = async () => {
  try {
    isLoading.value = true
    error.value = null

    if (isSharedPlaylist.value) {
      // Handle shared playlist
      await loadSharedPlaylist()
    } else {
      // Handle regular playlist
      await loadRegularPlaylist()
    }
  } catch (err) {
    console.error('Error loading playlist:', err)
    error.value = 'เกิดข้อผิดพลาดในการโหลดเพลย์ลิสต์'
  } finally {
    isLoading.value = false
  }
}

const loadRegularPlaylist = async () => {
  const decodedName = decodeURIComponent(playlistName)
  const foundPlaylist = playlist.getPlaylistByName(decodedName)

  if (!foundPlaylist) {
    error.value = 'ไม่พบเพลย์ลิสต์ที่ต้องการ'

    return
  }

  currentPlaylist.value = foundPlaylist
  editPlaylistName.value = foundPlaylist.name

  // Load songs data
  await loadSongsData(foundPlaylist.songIds)
}

const loadSharedPlaylist = async () => {
  const sharedData = playlist.parseSharedPlaylist(
    playlistName,
    route.query.ids as string,
  )

  if (!sharedData) {
    error.value = 'ลิงก์แชร์ไม่ถูกต้อง'

    return
  }

  // Create temporary playlist object for shared playlist
  currentPlaylist.value = {
    id: 'shared',
    name: sharedData.name,
    songIds: sharedData.songIds,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }

  loadSongsData(sharedData.songIds)
}

const loadSongsData = async (songIds: string[]) => {
  song.fetchPage(1, '', {
    params: {
      include: songIds.join(','),
    },
  })
}

useWatchTrue(() => song.fetch.status.isSuccess, () => {
  playlistSongs.value = song.fetch.items
})

// Methods
const getPlaylistActions = () => {
  if (isSharedPlaylist.value) {
    return []
  }

  return [
    {
      label: 'แก้ไขชื่อ',
      icon: 'i-lucide-edit',
      onSelect: () => {
        showEditModal.value = true
      },
    },
    {
      type: 'separator' as const,
    },
    {
      label: 'ลบเพลย์ลิสต์',
      icon: 'i-lucide-trash-2',
      color: 'error' as const,
      onSelect: confirmDeletePlaylist,
    },
  ]
}

const onSongReorder = async () => {
  if (!currentPlaylist.value || isSharedPlaylist.value) return

  const newOrder = playlistSongs.value.map((song) => song.id)
  const success = await playlist.reorderPlaylistSongs(currentPlaylist.value.id, newOrder)

  if (!success) {
    noti.error({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถเรียงลำดับเพลงได้ กรุณาลองใหม่อีกครั้ง',
    })

    // Reload to restore original order
    await loadPlaylistData()
  }
}

const confirmRemoveSong = (song: PlaylistSong, index: number) => {
  dialog.confirm({
    title: 'ยืนยันการลบ',
    description: `คุณต้องการลบเพลง "${song.title}" ออกจากเพลย์ลิสต์หรือไม่?`,
    confirmText: 'ลบ',
    cancelText: 'ยกเลิก',
  }).then(() => {
    removeSong(song.id, index)
  })
}

const removeSong = async (songId: string, index: number) => {
  if (!currentPlaylist.value || isSharedPlaylist.value) return

  const success = await playlist.removeSongFromPlaylist(currentPlaylist.value.id, songId)

  if (success) {
    playlistSongs.value.splice(index, 1)
    noti.success({
      title: 'สำเร็จ',
      description: 'ลบเพลงออกจากเพลย์ลิสต์เรียบร้อยแล้ว',
    })
  } else {
    noti.error({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถลบเพลงได้ กรุณาลองใหม่อีกครั้ง',
    })
  }
}

const sharePlaylist = async () => {
  if (!currentPlaylist.value) return

  if (currentPlaylist.value.songIds.length === 0) {
    noti.warning({
      title: 'ไม่สามารถแชร์ได้',
      description: 'เพลย์ลิสต์นี้ยังไม่มีเพลง กรุณาเพิ่มเพลงก่อนแชร์',
    })

    return
  }

  const shareUrl = playlist.generateShareUrl(currentPlaylist.value)

  try {
    await navigator.clipboard.writeText(shareUrl)
    noti.success({
      title: 'คัดลอกลิงก์แล้ว',
      description: 'ลิงก์แชร์เพลย์ลิสต์ถูกคัดลอกไปยังคลิปบอร์ดแล้ว',
    })
  } catch {
    noti.info({
      title: 'ลิงก์แชร์',
      description: shareUrl,
    })
  }
}

const updatePlaylistName = async () => {
  if (!currentPlaylist.value || !editPlaylistName.value.trim() || isSharedPlaylist.value) return

  isUpdating.value = true

  try {
    const success = await playlist.updatePlaylist(currentPlaylist.value.id, {
      name: editPlaylistName.value.trim(),
    })

    if (success) {
      currentPlaylist.value.name = editPlaylistName.value.trim()
      showEditModal.value = false

      noti.success({
        title: 'สำเร็จ',
        description: 'แก้ไขชื่อเพลย์ลิสต์เรียบร้อยแล้ว',
      })

      // Navigate to new URL
      await navigateTo(`/playlists/${encodeURIComponent(editPlaylistName.value.trim())}`)
    } else {
      noti.error({
        title: 'เกิดข้อผิดพลาด',
        description: 'ไม่สามารถแก้ไขชื่อเพลย์ลิสต์ได้ กรุณาลองใหม่อีกครั้ง',
      })
    }
  } finally {
    isUpdating.value = false
  }
}

const confirmDeletePlaylist = () => {
  if (!currentPlaylist.value || isSharedPlaylist.value) return

  dialog.confirm({
    title: 'ยืนยันการลบ',
    description: `คุณต้องการลบเพลย์ลิสต์ "${currentPlaylist.value.name}" หรือไม่? การดำเนินการนี้ไม่สามารถย้อนกลับได้`,
    confirmText: 'ลบ',
    cancelText: 'ยกเลิก',
  }).then(() => {
    deletePlaylist()
  })
}

const deletePlaylist = async () => {
  if (!currentPlaylist.value || isSharedPlaylist.value) return

  const success = await playlist.deletePlaylist(currentPlaylist.value.id)

  if (success) {
    noti.success({
      title: 'สำเร็จ',
      description: 'ลบเพลย์ลิสต์เรียบร้อยแล้ว',
    })

    await navigateTo('/playlists')
  } else {
    noti.error({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถลบเพลย์ลิสต์ได้ กรุณาลองใหม่อีกครั้ง',
    })
  }
}

const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString)

    return date.toLocaleDateString('th-TH', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  } catch {
    return 'ไม่ทราบ'
  }
}

// Initialize
onMounted(async () => {
  await loadPlaylistData()
})

// Watch for route changes (for shared playlists)
watch(() => route.query.ids, async () => {
  if (isSharedPlaylist.value) {
    await loadPlaylistData()
  }
})
</script>

<style scoped>
.ghost {
  opacity: 0.5;
}

.chosen {
  opacity: 0.8;
}

.drag {
  transform: rotate(5deg);
}

.drag-handle:hover {
  cursor: move;
}
</style>
